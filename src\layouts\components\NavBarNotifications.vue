<script lang="ts" setup>
import { useNotificationsStore } from '@/@core/stores/notifications'

// 使用全局通知 store
const notificationsStore = useNotificationsStore()

// 从 store 中获取响应式数据
const { notifications } = storeToRefs(notificationsStore)

// 转换为可变类型以避免类型错误
const notificationsList = computed(() => [...notifications.value])

// 移除/忽略通知
const removeNotification = async (notificationId: number) => {
  await notificationsStore.ignoreEvent(notificationId)
}

// 标记为已读
const markRead = async (notificationId: number) => {
  await notificationsStore.markEventAsRead(notificationId)
}

// 点击通知时标记为已读
const handleNotificationClick = async (notification: any) => {
  await notificationsStore.markEventAsRead(notification.event_id)
}

// 组件挂载时检查数据状态
onMounted(async () => {
  // 如果还没有数据，立即获取一次
  if (notifications.value.length === 0)
    await notificationsStore.fetchEventList()

  // 确保自动更新已启动（如果还没有启动的话）
  notificationsStore.startAutoUpdate()
})

// 注意：不在这里停止自动更新，因为其他页面可能也需要使用
// 自动更新应该在整个应用生命周期内保持运行
</script>

<template>
  <Notifications
    :notifications="notificationsList"
    @ignore="removeNotification"
    @read="markRead"
    @click:notification="handleNotificationClick"
  />
</template>
